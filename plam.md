 "Design a modern, sleek UI for a summer drinks website. The theme should be refreshing and vibra
nt, using cool tones like turquoise, lemon yellow, icy blue, and tropical gradients. Include a clean
 navbar with logo, featured drink section with high-quality images, card layout for multiple drinks,
 parallax scrolling banners with fruit slices, a smooth animated 'Order Now' button, and a footer wi
th social media links and newsletter signup. Use glassmorphism, shadows, and minimal rounded UI comp
onents. Typography should be fun, energetic, and easy to read. Mobile responsive layout, smooth tran
sitions."
Complete and enhance the Python AI coding agent by implementing all missing functionality and upgrading it to match the capabilities of leading AI coding assistants. Specifically:

**Core Requirements:**
1. **Complete Missing Logic**: Identify and implement any incomplete or placeholder functions in the current agent codebase
2. **Large Codebase Support**: Ensure the agent can handle enterprise-scale projects with thousands of files and complex dependencies
3. **Advanced Capabilities**: Add sophisticated features for code generation, refactoring, debugging, and project management

**Benchmark Against Leading AI Coding Agents:**
Research and implement capabilities comparable to:
- **Claude Code**: Advanced code understanding, multi-file context awareness, intelligent refactoring
- **Cursor Agent**: Real-time code completion, predictive editing, seamless IDE integration
- **Warp Agent**: Terminal integration, command prediction, workflow automation
- **GitHub Copilot**: Context-aware code suggestions, pattern recognition, multi-language support

**Specific Enhancement Areas:**
1. **Intelligence & Context**: 
   - Multi-file context understanding across large codebases
   - Semantic code analysis and relationship mapping
   - Intelligent code completion and suggestions

2. **Project Management**:
   - Full project scaffolding and architecture planning
   - Dependency management and conflict resolution
   - Build system integration and automation

3. **Advanced Code Operations**:
   - Sophisticated refactoring with safety checks
   - Automated testing and quality assurance
   - Performance optimization and code analysis

4. **User Experience**:
   - Intuitive natural language interface
   - Real-time streaming responses with progress tracking
   - Smart error handling and recovery suggestions

5. **Integration & Extensibility**:
   - Plugin architecture for custom tools
   - API integrations with development services
   - Version control system deep integration

**Success Criteria:**
- Agent can autonomously create, modify, and maintain large-scale software projects
- Matches or exceeds the functionality of commercial AI coding assistants
- Provides intelligent, context-aware assistance for complex development tasks
- Maintains high performance and reliability across diverse programming languages and frameworks

Ensure all implementations use production-ready code with proper error handling, documentation, and testing.Please complete the following tasks for both the Python and Go AI agents first see the requirements then  make and tasks list the do it:

**PART 1: Fix Go Agent Compilation Errors**
1. Fix the unused import error in `go-agent/internal/project/manager.go:7:2` - remove the unused "strings" import
2. Fix duplicate method declarations:
   - Remove duplicate `GetCurrentModel()` method in `internal/providers/gemini.go` (keep only one, preferably the one at line 284)
   - Remove duplicate `GetCurrentModel()` method in `internal/providers/mistral.go` (keep only one, preferably the one at line 259)  
   - Remove duplicate `GetCurrentModel()` method in `internal/providers/openai.go` (keep only one, preferably the one at line 194)
3. Ensure the Go agent compiles and runs without errors after these fixes

**PART 2: Add NVIDIA Provider Integration**
1. **For Python Agent:**
   - Add a new NVIDIA provider class that uses the OpenAI-compatible API
   - Base URL: `https://integrate.api.nvidia.com/v1`
   - API Key: `**********************************************************************`
   - Implement streaming support as shown in the reference code
   - Add support for reasoning content extraction from responses

2. **For Go Agent:**
   - Create a new NVIDIA provider using HTTP requests library (similar to existing providers)
   - Implement the same OpenAI-compatible API integration
   - Add streaming response support
   - Handle both regular content and reasoning content from responses

3. **Model Integration:**
   - Add all NVIDIA models listed in the `models.md` file to both agents
   - Ensure models are properly categorized and accessible through the `/models` command
   - Include the example model `deepseek-ai/deepseek-r1-0528` and all others from models.md

4. **Configuration Setup:**
   - Set the NVIDIA API key globally in both agents' configuration systems
   - Make the NVIDIA provider accessible through the existing `/switch` command (or `/providers` command in Go)
   - Ensure the provider can be selected and used seamlessly like other providers

5. **Testing:**
   - Verify that both agents can successfully switch to and use the NVIDIA provider
   - Test streaming responses and reasoning content extraction
   - Confirm all NVIDIA models are available and functional

**Reference Implementation Details:**
- Use the provided OpenAI client pattern with custom base_url
- Implement proper error handling for NVIDIA API responses
- Support temperature, top_p, max_tokens, and streaming parameters
- Handle both reasoning_content and regular content in streaming responsesFix and enhance the Go language AI agent with the following specific requirements:

**Core Issues to Fix:**
1. **AI Provider Switching Problem**: The Go agent is not properly switching between different AI models and providers - it's stuck using only OpenAI. Fix the provider switching mechanism to support all available AI providers (Gemini, Mistral, DeepSeek, Groq, Together AI, Claude, etc.)

2. **Default Configuration**: Set Gemini as the default AI provider with the `gemini-2.0-flash-lite` model as the default model selection

**Enhancement Requirements:**
1. **Complete Missing Functionality**: Identify and implement all missing or incomplete features in the Go agent to match the Python agent's capabilities

2. **UI Interface Enhancement**: Transform the Go agent's interface to match the OpenCode AI Terminal configuration agent style with:
   - Professional terminal UI with rich formatting
   - Interactive configuration menus
   - Clear command structure and help system
   - Status indicators and progress bars

3. **Streaming Response System**: Implement proper response streaming functionality that:
   - Shows real-time progress for long operations
   - Displays streaming text responses character by character
   - Handles interruptions gracefully
   - Provides visual feedback during processing

4. **Full Feature Parity**: Ensure the Go agent has all the advanced features present in the Python agent:
   - Multi-provider AI support with seamless switching
   - Advanced project management commands
   - Code analysis and refactoring capabilities
   - Plugin architecture support
   - Performance monitoring
   - Error recovery systems

**Technical Specifications:**
- Fix the AI provider manager to properly handle model switching
- Implement configuration persistence for default settings
- Add comprehensive error handling for API failures
- Ensure all 154+ tools are properly integrated and functional
- Test all AI providers to confirm they work correctly

**Success Criteria:**
- User can switch between any AI provider/model combination seamlessly
- Gemini with gemini-2.0-flash-lite works as the default
- Interface matches the professional quality of OpenCode AI Terminal
- Streaming responses work smoothly without lag or errors
- All missing functionality is implemented and tested

