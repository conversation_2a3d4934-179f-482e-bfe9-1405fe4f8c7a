```python
def greet_user(user_name: str = "User") -> str:
    """
    Greets the user with a personalized message.

    Args:
        user_name: The name of the user to greet. Defaults to "User" if no name is provided.

    Returns:
        A friendly greeting string.
    
    Raises:
        TypeError: If user_name is not a string.
        ValueError: If user_name is an empty string.
    """

    # Input validation: Check if the user_name is a string
    if not isinstance(user_name, str):
        raise TypeError("user_name must be a string.")

    # Input validation: Check if the user_name is empty
    if not user_name:
        raise ValueError("user_name cannot be an empty string.")

    try:
        # Construct the greeting message
        greeting = f"Hello, {user_name}! Welcome!"
        return greeting
    except Exception as e:
        # Handle any unexpected errors during greeting construction
        print(f"An error occurred while creating the greeting: {e}")
        return "Hello! Welcome!"  # Provide a default greeting in case of error


if __name__ == "__main__":
    """
    Example usage of the greet_user function.
    """
    try:
        # Get user's name from input
        name = input("Please enter your name: ")

        # Greet the user using the function
        greeting_message = greet_user(name)
        print(greeting_message)

        # Example with default user name
        default_greeting = greet_user()
        print(default_greeting)

    except ValueError as ve:
        print(f"Error: {ve}")
    except TypeError as te:
        print(f"Error: {te}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
```