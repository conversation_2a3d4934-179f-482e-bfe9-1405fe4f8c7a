2025-07-14 14:20:55,657 - root - ERROR - Fatal error: 'Layout' object does not support item assignment
2025-07-14 14:20:55,662 - root - ERROR - Fatal error: 'Layout' object does not support item assignment
2025-07-14 14:54:28,924 - root - ERROR - Process message error: 'AdvancedCodingAgent' object has no attribute 'edit_file'
2025-07-14 14:54:48,258 - root - ERROR - Process message error: 'AdvancedCodingAgent' object has no attribute 'create_file'
2025-07-14 14:55:45,417 - root - ERROR - Process message error: 'AdvancedCodingAgent' object has no attribute 'plan_next_step'
2025-07-14 14:58:39,280 - root - ERROR - Process message error: 'AdvancedCodingAgent' object has no attribute 'create_file'
2025-07-14 15:15:17,175 - root - ERROR - Process message error: Got unsupported early_stopping_method `generate`
2025-07-18 17:57:36,102 - root - ERROR - Process message error: 503 The model is overloaded. Please try again later.
2025-07-18 18:38:37,375 - backoff - INFO - Backing off make_request_with_retry(...) for 0.2s (google.api_core.exceptions.ServiceUnavailable: 503 The model is overloaded. Please try again later.)
2025-07-18 18:38:57,371 - backoff - INFO - Backing off make_request_with_retry(...) for 0.5s (google.api_core.exceptions.ServiceUnavailable: 503 The model is overloaded. Please try again later.)
2025-07-18 18:39:09,173 - backoff - ERROR - Giving up make_request_with_retry(...) after 3 tries (google.api_core.exceptions.ServiceUnavailable: 503 The model is overloaded. Please try again later.)
2025-07-18 18:39:32,943 - backoff - INFO - Backing off make_request_with_retry(...) for 0.9s (google.api_core.exceptions.ServiceUnavailable: 503 The model is overloaded. Please try again later.)
2025-07-18 18:39:50,358 - backoff - INFO - Backing off make_request_with_retry(...) for 1.8s (google.api_core.exceptions.ServiceUnavailable: 503 The model is overloaded. Please try again later.)
2025-07-18 18:42:53,268 - backoff - INFO - Backing off make_request_with_retry(...) for 0.9s (google.api_core.exceptions.ServiceUnavailable: 503 The model is overloaded. Please try again later.)
2025-07-18 19:17:01,584 - root - ERROR - Process message error: Prompt missing required variables: {'tool_names'}
2025-07-18 19:17:02,664 - root - ERROR - Process message error: Prompt missing required variables: {'tool_names'}
2025-07-18 19:17:03,742 - root - ERROR - Process message error: Prompt missing required variables: {'tool_names'}
2025-07-18 19:17:15,073 - root - ERROR - Process message error: Prompt missing required variables: {'tool_names'}
2025-07-18 19:17:16,150 - root - ERROR - Process message error: Prompt missing required variables: {'tool_names'}
2025-07-18 19:17:17,234 - root - ERROR - Process message error: Prompt missing required variables: {'tool_names'}
2025-07-18 19:30:38,730 - backoff - INFO - Backing off make_request_with_retry(...) for 0.7s (ValueError: Got unsupported early_stopping_method `generate`)
2025-07-18 23:11:58,667 - backoff - INFO - Backing off make_request_with_retry(...) for 1.0s (ValueError: Got unsupported early_stopping_method `generate`)
2025-07-18 23:12:25,926 - langchain_google_genai.chat_models - WARNING - Retrying langchain_google_genai.chat_models._chat_with_retry.<locals>._chat_with_retry in 2.0 seconds as it raised ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 15
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 33
}
].
2025-07-18 23:12:28,332 - backoff - ERROR - Giving up make_request_with_retry(...) after 2 tries (ValueError: Got unsupported early_stopping_method `generate`)
2025-07-18 23:12:28,334 - root - ERROR - Process message error: Got unsupported early_stopping_method `generate`
2025-07-18 23:12:54,770 - backoff - INFO - Backing off make_request_with_retry(...) for 0.8s (ValueError: Got unsupported early_stopping_method `generate`)
2025-07-18 23:13:23,396 - langchain_google_genai.chat_models - WARNING - Retrying langchain_google_genai.chat_models._chat_with_retry.<locals>._chat_with_retry in 2.0 seconds as it raised ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 15
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 35
}
].
2025-07-18 23:13:25,758 - backoff - INFO - Backing off make_request_with_retry(...) for 0.8s (ValueError: Got unsupported early_stopping_method `generate`)
2025-07-18 23:13:27,182 - langchain_google_genai.chat_models - WARNING - Retrying langchain_google_genai.chat_models._chat_with_retry.<locals>._chat_with_retry in 2.0 seconds as it raised ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 15
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 32
}
].
2025-07-18 23:14:11,897 - backoff - ERROR - Giving up make_request_with_retry(...) after 3 tries (ValueError: Got unsupported early_stopping_method `generate`)
2025-07-18 23:14:11,898 - root - ERROR - Process message error: Got unsupported early_stopping_method `generate`
2025-07-18 23:14:30,523 - backoff - INFO - Backing off make_request_with_retry(...) for 0.1s (ValueError: Got unsupported early_stopping_method `generate`)
2025-07-18 23:14:47,840 - backoff - INFO - Backing off make_request_with_retry(...) for 0.0s (ValueError: Got unsupported early_stopping_method `generate`)
2025-07-19 09:43:14,241 - backoff - INFO - Backing off make_request_with_retry(...) for 0.2s (ValueError: Got unsupported early_stopping_method `generate`)
2025-07-19 10:11:11,704 - langchain_google_genai.chat_models - WARNING - Retrying langchain_google_genai.chat_models._chat_with_retry.<locals>._chat_with_retry in 2.0 seconds as it raised ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 200
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 48
}
].
2025-07-19 10:11:16,939 - langchain_google_genai.chat_models - WARNING - Retrying langchain_google_genai.chat_models._chat_with_retry.<locals>._chat_with_retry in 2.0 seconds as it raised ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 200
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 43
}
].
2025-07-19 10:11:22,502 - langchain_google_genai.chat_models - WARNING - Retrying langchain_google_genai.chat_models._chat_with_retry.<locals>._chat_with_retry in 2.0 seconds as it raised ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 200
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 37
}
].
2025-07-19 10:11:25,138 - root - ERROR - Agent execution error: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 200
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 34
}
]
2025-07-19 10:11:26,283 - langchain_google_genai.chat_models - WARNING - Retrying langchain_google_genai.chat_models._chat_with_retry.<locals>._chat_with_retry in 2.0 seconds as it raised ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 200
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 33
}
].
2025-07-19 10:11:28,663 - root - ERROR - Fallback response error: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 200
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 31
}
]
2025-07-19 10:11:52,414 - langchain_google_genai.chat_models - WARNING - Retrying langchain_google_genai.chat_models._chat_with_retry.<locals>._chat_with_retry in 2.0 seconds as it raised ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 200
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 7
}
].
2025-07-19 10:11:58,692 - langchain_google_genai.chat_models - WARNING - Retrying langchain_google_genai.chat_models._chat_with_retry.<locals>._chat_with_retry in 2.0 seconds as it raised ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 200
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 1
}
].
2025-07-19 10:12:05,659 - langchain_google_genai.chat_models - WARNING - Retrying langchain_google_genai.chat_models._chat_with_retry.<locals>._chat_with_retry in 2.0 seconds as it raised ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 200
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 54
}
].
2025-07-19 10:22:37,212 - langchain_google_genai.chat_models - WARNING - Retrying langchain_google_genai.chat_models._chat_with_retry.<locals>._chat_with_retry in 2.0 seconds as it raised ResourceExhausted: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-2.0-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 200
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 22
}
].
2025-07-19 11:08:07,820 - root - ERROR - Agent error: 'AdvancedCodingAgent' object has no attribute 'handle_project_command'
2025-07-19 14:09:01,124 - root - ERROR - Process message error: Prompt missing required variables: {'tool_names'}
2025-07-19 14:09:02,201 - root - ERROR - Process message error: Prompt missing required variables: {'tool_names'}
2025-07-19 14:09:03,283 - root - ERROR - Process message error: Prompt missing required variables: {'tool_names'}
2025-07-19 14:09:03,288 - root - ERROR - Agent error: unable to open database file
2025-07-19 14:20:33,541 - backoff - INFO - Backing off make_request_with_retry(...) for 0.9s (ValueError: Got unsupported early_stopping_method `generate`)
