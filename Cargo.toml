[package]
name = "ultra-powerful-agent"
version = "1.0.0"
edition = "2021"
authors = ["AI Agent Team"]
description = "Ultra-Powerful AI Coding Agent - High-Performance Rust Implementation"
license = "MIT"

[[bin]]
name = "agent"
path = "agent.rs"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.11", features = ["json"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
regex = "1.0"
clap = { version = "4.0", features = ["derive"] }
colored = "2.0"
indicatif = "0.17"
crossterm = "0.27"
ratatui = "0.24"

[dev-dependencies]
tokio-test = "0.4"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
opt-level = 1
